import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/Colors';
import { LiveWorkoutData, WorkoutOverlayConfig } from '../../types/social';
import GlassContainer from '../ui/GlassContainer';

interface LiveWorkoutOverlayProps {
  workoutData: LiveWorkoutData;
  config: WorkoutOverlayConfig;
  isVisible: boolean;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function LiveWorkoutOverlay({
  workoutData,
  config,
  isVisible
}: LiveWorkoutOverlayProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Animate overlay visibility
  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: config.opacity,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -100,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, config.opacity]);

  // Pulse animation for active elements
  useEffect(() => {
    if (workoutData.currentExercise && workoutData.currentExercise.restTimeRemaining) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [workoutData.currentExercise?.restTimeRemaining]);

  const getOverlayPosition = () => {
    const margin = 20;
    const overlayWidth = config.size === 'small' ? 280 : config.size === 'medium' ? 350 : 420;
    const overlayHeight = config.size === 'small' ? 120 : config.size === 'medium' ? 150 : 180;

    switch (config.position) {
      case 'top-left':
        return { top: margin, left: margin };
      case 'top-right':
        return { top: margin, right: margin };
      case 'bottom-left':
        return { bottom: margin, left: margin };
      case 'bottom-right':
        return { bottom: margin, right: margin };
      case 'center':
        return {
          top: (screenHeight - overlayHeight) / 2,
          left: (screenWidth - overlayWidth) / 2
        };
      default:
        return { top: margin, right: margin };
    }
  };

  const getOverlaySize = () => {
    switch (config.size) {
      case 'small':
        return { width: 280, minHeight: 120 };
      case 'medium':
        return { width: 350, minHeight: 150 };
      case 'large':
        return { width: 420, minHeight: 180 };
      default:
        return { width: 350, minHeight: 150 };
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getThemeColors = () => {
    if (config.customBranding?.colors) {
      return config.customBranding.colors;
    }

    switch (config.theme) {
      case 'light':
        return {
          primary: '#FFFFFF',
          secondary: '#F5F5F5',
          text: '#000000',
          background: 'rgba(255, 255, 255, 0.9)',
        };
      case 'transparent':
        return {
          primary: Colors.accent.primary,
          secondary: Colors.accent.secondary,
          text: Colors.text.primary,
          background: 'rgba(0, 0, 0, 0.3)',
        };
      default: // dark
        return {
          primary: Colors.accent.primary,
          secondary: Colors.accent.secondary,
          text: Colors.text.primary,
          background: 'rgba(0, 0, 0, 0.8)',
        };
    }
  };

  const themeColors = getThemeColors();
  const overlayPosition = getOverlayPosition();
  const overlaySize = getOverlaySize();

  if (!isVisible) return null;

  return (
    <Animated.View
      style={[
        styles.overlay,
        overlayPosition,
        overlaySize,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
          backgroundColor: themeColors.background,
        },
      ]}
    >
      <GlassContainer
        style={[styles.container, { backgroundColor: 'transparent' }]}
        intensity={config.theme === 'transparent' ? 'light' : 'medium'}
      >
        {/* Header with branding */}
        <View style={styles.header}>
          {config.customBranding?.logo && (
            <View style={styles.logo}>
              {/* Logo would be rendered here */}
              <Ionicons name="fitness" size={20} color={themeColors.primary} />
            </View>
          )}
          <Text style={[styles.brandText, { color: themeColors.text }]}>
            EVERFIT LIVE
          </Text>
          {config.showTimer && (
            <Text style={[styles.timerText, { color: themeColors.secondary }]}>
              {formatDuration(workoutData.workoutStats.duration)}
            </Text>
          )}
        </View>

        {/* Current Exercise */}
        {config.showCurrentExercise && workoutData.currentExercise && (
          <View style={styles.exerciseSection}>
            <Text style={[styles.exerciseLabel, { color: themeColors.secondary }]}>
              CURRENT EXERCISE
            </Text>
            <Text style={[styles.exerciseName, { color: themeColors.text }]}>
              {workoutData.currentExercise.name}
            </Text>

            {config.showCurrentSet && (
              <View style={styles.setInfo}>
                <Text style={[styles.setText, { color: themeColors.text }]}>
                  Set {workoutData.currentExercise.currentSet} of {workoutData.currentExercise.totalSets}
                </Text>
                {workoutData.currentExercise.weight && workoutData.currentExercise.reps && (
                  <Text style={[styles.weightReps, { color: themeColors.primary }]}>
                    {workoutData.currentExercise.weight}kg × {workoutData.currentExercise.reps}
                  </Text>
                )}
              </View>
            )}

            {/* Rest Timer */}
            {workoutData.currentExercise.restTimeRemaining && workoutData.currentExercise.restTimeRemaining > 0 && (
              <Animated.View
                style={[
                  styles.restTimer,
                  {
                    backgroundColor: themeColors.primary,
                    transform: [{ scale: pulseAnim }]
                  }
                ]}
              >
                <Ionicons name="timer" size={16} color={themeColors.background} />
                <Text style={[styles.restText, { color: themeColors.background }]}>
                  Rest: {formatTime(workoutData.currentExercise.restTimeRemaining)}
                </Text>
              </Animated.View>
            )}
          </View>
        )}

        {/* Workout Stats */}
        <View style={styles.statsSection}>
          <View style={styles.statRow}>
            {config.showWorkoutProgress && (
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: themeColors.secondary }]}>PROGRESS</Text>
                <Text style={[styles.statValue, { color: themeColors.text }]}>
                  {workoutData.workoutProgress.percentage}%
                </Text>
              </View>
            )}

            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: themeColors.secondary }]}>VOLUME</Text>
              <Text style={[styles.statValue, { color: themeColors.text }]}>
                {(workoutData.workoutStats.totalVolume / 1000).toFixed(1)}k kg
              </Text>
            </View>

            {config.showCalories && workoutData.workoutStats.caloriesBurned && (
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: themeColors.secondary }]}>CALORIES</Text>
                <Text style={[styles.statValue, { color: themeColors.text }]}>
                  {workoutData.workoutStats.caloriesBurned}
                </Text>
              </View>
            )}

            {config.showHeartRate && workoutData.workoutStats.heartRate && (
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: themeColors.secondary }]}>HR</Text>
                <Text style={[styles.statValue, { color: themeColors.primary }]}>
                  {workoutData.workoutStats.heartRate} bpm
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Personal Records */}
        {config.showPersonalRecords && workoutData.workoutStats.personalRecords.length > 0 && (
          <View style={styles.prSection}>
            <View style={styles.prHeader}>
              <Ionicons name="trophy" size={16} color={themeColors.primary} />
              <Text style={[styles.prText, { color: themeColors.primary }]}>
                {workoutData.workoutStats.personalRecords.length} PR{workoutData.workoutStats.personalRecords.length > 1 ? 's' : ''}!
              </Text>
            </View>
          </View>
        )}
      </GlassContainer>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  container: {
    padding: 12,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  logo: {
    marginRight: 8,
  },
  brandText: {
    fontSize: 12,
    fontWeight: '700',
    letterSpacing: 1,
    flex: 1,
  },
  timerText: {
    fontSize: 12,
    fontWeight: '600',
  },
  exerciseSection: {
    marginBottom: 8,
  },
  exerciseLabel: {
    fontSize: 10,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: 2,
  },
  exerciseName: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  setInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  setText: {
    fontSize: 12,
    fontWeight: '500',
  },
  weightReps: {
    fontSize: 12,
    fontWeight: '700',
  },
  restTimer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  restText: {
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 4,
  },
  statsSection: {
    marginBottom: 8,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statLabel: {
    fontSize: 9,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: 2,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '700',
  },
  prSection: {
    marginTop: 4,
  },
  prHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  prText: {
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 4,
  },
});
