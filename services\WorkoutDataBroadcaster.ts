import { LiveWorkoutData, WorkoutOverlayConfig } from '../types/social';
import { WorkoutExercise, WorkoutSet } from '../types/workout';
import TwitchService from './TwitchService';

export interface WorkoutDataBroadcasterConfig {
  streamId: string;
  overlayConfig: WorkoutOverlayConfig;
  broadcastInterval: number; // milliseconds
  enableTwitchIntegration: boolean;
}

class WorkoutDataBroadcaster {
  private static instance: WorkoutDataBroadcaster;
  private isActive: boolean = false;
  private config: WorkoutDataBroadcasterConfig | null = null;
  private broadcastTimer: NodeJS.Timeout | null = null;
  private twitchService: TwitchService;
  
  // Current workout state
  private currentWorkout: WorkoutExercise[] = [];
  private workoutStartTime: Date | null = null;
  private currentExerciseIndex: number = 0;
  private currentSetIndex: number = 0;
  private restTimerStart: Date | null = null;
  private restDuration: number = 0;
  private personalRecords: string[] = [];
  private totalVolume: number = 0;
  private caloriesBurned: number = 0;
  private heartRate: number = 0;
  private streamerNotes: string = '';

  // Listeners for workout events
  private listeners: Map<string, (data: LiveWorkoutData) => void> = new Map();

  public static getInstance(): WorkoutDataBroadcaster {
    if (!WorkoutDataBroadcaster.instance) {
      WorkoutDataBroadcaster.instance = new WorkoutDataBroadcaster();
    }
    return WorkoutDataBroadcaster.instance;
  }

  constructor() {
    this.twitchService = TwitchService.getInstance();
  }

  /**
   * Start broadcasting workout data
   */
  async startBroadcasting(config: WorkoutDataBroadcasterConfig): Promise<void> {
    if (this.isActive) {
      throw new Error('Broadcaster is already active');
    }

    this.config = config;
    this.isActive = true;
    this.workoutStartTime = new Date();

    // Connect to overlay WebSocket if enabled
    if (config.overlayConfig.enabled) {
      try {
        await this.twitchService.connectToOverlay(config.streamId);
        console.log('Connected to overlay WebSocket');
      } catch (error) {
        console.error('Failed to connect to overlay:', error);
      }
    }

    // Start broadcasting timer
    this.broadcastTimer = setInterval(() => {
      this.broadcastCurrentData();
    }, config.broadcastInterval);

    console.log('Workout data broadcasting started');
  }

  /**
   * Stop broadcasting workout data
   */
  stopBroadcasting(): void {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    
    if (this.broadcastTimer) {
      clearInterval(this.broadcastTimer);
      this.broadcastTimer = null;
    }

    // Disconnect from overlay
    this.twitchService.disconnectFromOverlay();

    // Reset state
    this.resetWorkoutState();

    console.log('Workout data broadcasting stopped');
  }

  /**
   * Update current workout data
   */
  updateWorkout(workout: WorkoutExercise[]): void {
    this.currentWorkout = workout;
    this.calculateTotalVolume();
    this.broadcastCurrentData();
  }

  /**
   * Update current exercise and set
   */
  updateCurrentPosition(exerciseIndex: number, setIndex: number): void {
    this.currentExerciseIndex = exerciseIndex;
    this.currentSetIndex = setIndex;
    this.broadcastCurrentData();
  }

  /**
   * Start rest timer
   */
  startRestTimer(duration: number): void {
    this.restTimerStart = new Date();
    this.restDuration = duration;
    this.broadcastCurrentData();
  }

  /**
   * Stop rest timer
   */
  stopRestTimer(): void {
    this.restTimerStart = null;
    this.restDuration = 0;
    this.broadcastCurrentData();
  }

  /**
   * Add personal record
   */
  addPersonalRecord(record: string): void {
    this.personalRecords.push(record);
    this.broadcastCurrentData();
  }

  /**
   * Update biometric data
   */
  updateBiometrics(heartRate?: number, caloriesBurned?: number): void {
    if (heartRate !== undefined) this.heartRate = heartRate;
    if (caloriesBurned !== undefined) this.caloriesBurned = caloriesBurned;
    this.broadcastCurrentData();
  }

  /**
   * Update streamer notes
   */
  updateStreamerNotes(notes: string): void {
    this.streamerNotes = notes;
    this.broadcastCurrentData();
  }

  /**
   * Add event listener for workout data updates
   */
  addListener(id: string, callback: (data: LiveWorkoutData) => void): void {
    this.listeners.set(id, callback);
  }

  /**
   * Remove event listener
   */
  removeListener(id: string): void {
    this.listeners.delete(id);
  }

  /**
   * Get current workout data
   */
  getCurrentWorkoutData(): LiveWorkoutData | null {
    if (!this.isActive || !this.config || !this.workoutStartTime) {
      return null;
    }

    return this.buildWorkoutData();
  }

  /**
   * Broadcast current workout data to all connected services
   */
  private broadcastCurrentData(): void {
    if (!this.isActive || !this.config) {
      return;
    }

    const workoutData = this.buildWorkoutData();

    // Send to overlay WebSocket
    if (this.config.overlayConfig.enabled) {
      this.twitchService.sendWorkoutDataToOverlay(workoutData);
    }

    // Notify local listeners
    this.listeners.forEach(callback => {
      try {
        callback(workoutData);
      } catch (error) {
        console.error('Error in workout data listener:', error);
      }
    });
  }

  /**
   * Build current workout data object
   */
  private buildWorkoutData(): LiveWorkoutData {
    const now = new Date();
    const duration = this.workoutStartTime ? 
      Math.floor((now.getTime() - this.workoutStartTime.getTime()) / 60000) : 0;

    let currentExercise = undefined;
    if (this.currentWorkout.length > 0 && this.currentExerciseIndex < this.currentWorkout.length) {
      const exercise = this.currentWorkout[this.currentExerciseIndex];
      const currentSet = exercise.sets[this.currentSetIndex];
      
      currentExercise = {
        name: this.getExerciseName(exercise.exerciseId),
        muscleGroups: this.getExerciseMuscleGroups(exercise.exerciseId),
        currentSet: this.currentSetIndex + 1,
        totalSets: exercise.sets.length,
        weight: currentSet?.weight,
        reps: currentSet?.reps,
        restTimeRemaining: this.getRemainingRestTime(),
      };
    }

    const completedExercises = this.currentWorkout.filter(exercise => 
      exercise.sets.every(set => set.completed)
    ).length;

    const workoutProgress = this.currentWorkout.length > 0 ? 
      (completedExercises / this.currentWorkout.length) * 100 : 0;

    const estimatedTimeRemaining = this.calculateEstimatedTimeRemaining();

    return {
      streamId: this.config!.streamId,
      timestamp: now,
      currentExercise,
      workoutStats: {
        duration,
        totalVolume: this.totalVolume,
        exercisesCompleted: completedExercises,
        totalExercises: this.currentWorkout.length,
        caloriesBurned: this.caloriesBurned > 0 ? this.caloriesBurned : undefined,
        heartRate: this.heartRate > 0 ? this.heartRate : undefined,
        personalRecords: [...this.personalRecords],
      },
      workoutProgress: {
        percentage: Math.round(workoutProgress),
        estimatedTimeRemaining,
      },
      streamerNotes: this.streamerNotes || undefined,
    };
  }

  /**
   * Calculate total volume from current workout
   */
  private calculateTotalVolume(): void {
    this.totalVolume = this.currentWorkout.reduce((total, exercise) => {
      return total + exercise.sets.reduce((setTotal, set) => {
        if (set.completed && set.weight && set.reps) {
          return setTotal + (set.weight * set.reps);
        }
        return setTotal;
      }, 0);
    }, 0);
  }

  /**
   * Get remaining rest time in seconds
   */
  private getRemainingRestTime(): number | undefined {
    if (!this.restTimerStart || this.restDuration === 0) {
      return undefined;
    }

    const elapsed = Math.floor((Date.now() - this.restTimerStart.getTime()) / 1000);
    const remaining = this.restDuration - elapsed;
    
    return remaining > 0 ? remaining : 0;
  }

  /**
   * Calculate estimated time remaining for workout
   */
  private calculateEstimatedTimeRemaining(): number {
    if (this.currentWorkout.length === 0) return 0;

    const completedSets = this.currentWorkout.reduce((total, exercise) => 
      total + exercise.sets.filter(set => set.completed).length, 0
    );

    const totalSets = this.currentWorkout.reduce((total, exercise) => 
      total + exercise.sets.length, 0
    );

    if (completedSets === 0) return 60; // Default estimate

    const currentDuration = this.workoutStartTime ? 
      (Date.now() - this.workoutStartTime.getTime()) / 60000 : 0;

    const averageTimePerSet = currentDuration / completedSets;
    const remainingSets = totalSets - completedSets;

    return Math.round(remainingSets * averageTimePerSet);
  }

  /**
   * Get exercise name by ID (mock implementation)
   */
  private getExerciseName(exerciseId: string): string {
    // This would typically fetch from a database or exercise library
    const exerciseNames: { [key: string]: string } = {
      '1': 'Bench Press',
      '2': 'Squat',
      '3': 'Deadlift',
      '4': 'Pull-ups',
      '5': 'Overhead Press',
    };
    return exerciseNames[exerciseId] || `Exercise ${exerciseId}`;
  }

  /**
   * Get exercise muscle groups by ID (mock implementation)
   */
  private getExerciseMuscleGroups(exerciseId: string): string[] {
    const exerciseMuscleGroups: { [key: string]: string[] } = {
      '1': ['chest', 'triceps'],
      '2': ['quadriceps', 'glutes'],
      '3': ['hamstrings', 'glutes', 'back'],
      '4': ['lats', 'biceps'],
      '5': ['shoulders', 'triceps'],
    };
    return exerciseMuscleGroups[exerciseId] || [];
  }

  /**
   * Reset workout state
   */
  private resetWorkoutState(): void {
    this.currentWorkout = [];
    this.workoutStartTime = null;
    this.currentExerciseIndex = 0;
    this.currentSetIndex = 0;
    this.restTimerStart = null;
    this.restDuration = 0;
    this.personalRecords = [];
    this.totalVolume = 0;
    this.caloriesBurned = 0;
    this.heartRate = 0;
    this.streamerNotes = '';
  }

  /**
   * Check if broadcaster is currently active
   */
  isActiveBroadcasting(): boolean {
    return this.isActive;
  }

  /**
   * Get current configuration
   */
  getCurrentConfig(): WorkoutDataBroadcasterConfig | null {
    return this.config;
  }
}

export default WorkoutDataBroadcaster;
