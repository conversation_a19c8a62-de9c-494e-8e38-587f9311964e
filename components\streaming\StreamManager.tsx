import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Switch,
  Alert,
  Modal,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/Colors';
import { TwitchStreamConfig, WorkoutOverlayConfig, LiveStream } from '../../types/social';
import GlassContainer from '../ui/GlassContainer';
import TwitchService from '../../services/TwitchService';

interface StreamManagerProps {
  visible: boolean;
  onClose: () => void;
  onStartStream: (config: TwitchStreamConfig & { overlayConfig: WorkoutOverlayConfig }) => void;
  currentStream?: LiveStream;
}

export default function StreamManager({
  visible,
  onClose,
  onStartStream,
  currentStream
}: StreamManagerProps) {
  const [activeTab, setActiveTab] = useState<'twitch' | 'overlay' | 'preview'>('twitch');
  const [isConnectedToTwitch, setIsConnectedToTwitch] = useState(false);
  const [twitchUser, setTwitchUser] = useState<any>(null);

  // Twitch Configuration
  const [twitchConfig, setTwitchConfig] = useState<TwitchStreamConfig>({
    enabled: false,
    streamTitle: 'Live Workout Session - Everfit',
    streamCategory: 'Just Chatting',
    streamTags: ['fitness', 'workout', 'health'],
    autoStartTwitch: false,
    syncWithTwitch: true,
    twitchChatIntegration: true,
    overlayEnabled: true,
  });

  // Overlay Configuration
  const [overlayConfig, setOverlayConfig] = useState<WorkoutOverlayConfig>({
    enabled: true,
    position: 'top-right',
    size: 'medium',
    opacity: 0.9,
    showCurrentExercise: true,
    showCurrentSet: true,
    showTimer: true,
    showHeartRate: false,
    showCalories: true,
    showPersonalRecords: true,
    showWorkoutProgress: true,
    theme: 'dark',
  });

  const twitchService = TwitchService.getInstance();

  useEffect(() => {
    // Check if user is already connected to Twitch
    checkTwitchConnection();
  }, []);

  const checkTwitchConnection = async () => {
    // This would check stored tokens and validate them
    // For now, we'll simulate the check
    const storedToken = null; // await AsyncStorage.getItem('twitch_access_token');
    if (storedToken) {
      const isValid = await twitchService.validateToken(storedToken);
      if (isValid) {
        setIsConnectedToTwitch(true);
        // Get user info
        try {
          const user = await twitchService.getCurrentUser(storedToken);
          setTwitchUser(user);
        } catch (error) {
          console.error('Error getting Twitch user:', error);
        }
      }
    }
  };

  const connectToTwitch = async () => {
    try {
      const authUrl = twitchService.generateAuthUrl();
      // In a real app, you'd open this URL in a browser or WebView
      Alert.alert(
        'Connect to Twitch',
        'This would open Twitch authentication in a browser. For demo purposes, we\'ll simulate a successful connection.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Simulate Connection',
            onPress: () => {
              setIsConnectedToTwitch(true);
              setTwitchUser({
                display_name: 'DemoStreamer',
                login: 'demostreamer',
                profile_image_url: 'https://placeholder.com/50x50',
              });
              setTwitchConfig(prev => ({ ...prev, enabled: true }));
            }
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to connect to Twitch');
    }
  };

  const disconnectFromTwitch = () => {
    Alert.alert(
      'Disconnect from Twitch',
      'Are you sure you want to disconnect from Twitch?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disconnect',
          style: 'destructive',
          onPress: () => {
            setIsConnectedToTwitch(false);
            setTwitchUser(null);
            setTwitchConfig(prev => ({ ...prev, enabled: false }));
          },
        },
      ]
    );
  };

  const startStream = () => {
    if (!twitchConfig.enabled && !overlayConfig.enabled) {
      Alert.alert('Error', 'Please enable at least Twitch streaming or overlay features');
      return;
    }

    onStartStream({
      ...twitchConfig,
      overlayConfig,
    });
    onClose();
  };

  const renderTwitchTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Twitch Connection Status */}
      <GlassContainer style={styles.section} intensity="medium">
        <View style={styles.sectionHeader}>
          <Ionicons name="logo-twitch" size={24} color="#9146FF" />
          <Text style={styles.sectionTitle}>Twitch Integration</Text>
        </View>

        {isConnectedToTwitch ? (
          <View style={styles.connectedSection}>
            <View style={styles.userInfo}>
              <View style={styles.userAvatar}>
                <Ionicons name="person" size={20} color={Colors.text.primary} />
              </View>
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{twitchUser?.display_name}</Text>
                <Text style={styles.userHandle}>@{twitchUser?.login}</Text>
              </View>
              <TouchableOpacity
                style={styles.disconnectButton}
                onPress={disconnectFromTwitch}
              >
                <Text style={styles.disconnectText}>Disconnect</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <TouchableOpacity style={styles.connectButton} onPress={connectToTwitch}>
            <Ionicons name="link" size={20} color={Colors.text.inverse} />
            <Text style={styles.connectButtonText}>Connect to Twitch</Text>
          </TouchableOpacity>
        )}
      </GlassContainer>

      {/* Stream Settings */}
      {isConnectedToTwitch && (
        <>
          <GlassContainer style={styles.section} intensity="medium">
            <Text style={styles.sectionTitle}>Stream Settings</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Stream Title</Text>
              <TextInput
                style={styles.textInput}
                value={twitchConfig.streamTitle}
                onChangeText={(text) => setTwitchConfig(prev => ({ ...prev, streamTitle: text }))}
                placeholder="Enter stream title..."
                placeholderTextColor={Colors.text.tertiary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Category</Text>
              <TouchableOpacity style={styles.categorySelector}>
                <Text style={styles.categoryText}>{twitchConfig.streamCategory}</Text>
                <Ionicons name="chevron-down" size={20} color={Colors.text.tertiary} />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Tags</Text>
              <View style={styles.tagsContainer}>
                {twitchConfig.streamTags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          </GlassContainer>

          <GlassContainer style={styles.section} intensity="medium">
            <Text style={styles.sectionTitle}>Advanced Options</Text>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Auto-start Twitch stream</Text>
              <Switch
                value={twitchConfig.autoStartTwitch}
                onValueChange={(value) => setTwitchConfig(prev => ({ ...prev, autoStartTwitch: value }))}
                trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                thumbColor={Colors.text.inverse}
              />
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Sync with Twitch</Text>
              <Switch
                value={twitchConfig.syncWithTwitch}
                onValueChange={(value) => setTwitchConfig(prev => ({ ...prev, syncWithTwitch: value }))}
                trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                thumbColor={Colors.text.inverse}
              />
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Enable Twitch chat integration</Text>
              <Switch
                value={twitchConfig.twitchChatIntegration}
                onValueChange={(value) => setTwitchConfig(prev => ({ ...prev, twitchChatIntegration: value }))}
                trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                thumbColor={Colors.text.inverse}
              />
            </View>
          </GlassContainer>
        </>
      )}
    </ScrollView>
  );

  const renderOverlayTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Overlay Settings */}
      <GlassContainer style={styles.section} intensity="medium">
        <View style={styles.sectionHeader}>
          <Ionicons name="layers" size={24} color={Colors.accent.primary} />
          <Text style={styles.sectionTitle}>Workout Overlay</Text>
          <Switch
            value={overlayConfig.enabled}
            onValueChange={(value) => setOverlayConfig(prev => ({ ...prev, enabled: value }))}
            trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
            thumbColor={Colors.text.inverse}
          />
        </View>

        {overlayConfig.enabled && (
          <>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Position</Text>
              <View style={styles.positionGrid}>
                {[
                  { key: 'top-left', label: 'Top Left' },
                  { key: 'top-right', label: 'Top Right' },
                  { key: 'bottom-left', label: 'Bottom Left' },
                  { key: 'bottom-right', label: 'Bottom Right' },
                  { key: 'center', label: 'Center' },
                ].map((position) => (
                  <TouchableOpacity
                    key={position.key}
                    style={[
                      styles.positionButton,
                      overlayConfig.position === position.key && styles.activePositionButton
                    ]}
                    onPress={() => setOverlayConfig(prev => ({ ...prev, position: position.key as any }))}
                  >
                    <Text style={[
                      styles.positionButtonText,
                      overlayConfig.position === position.key && styles.activePositionButtonText
                    ]}>
                      {position.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Size</Text>
              <View style={styles.sizeSelector}>
                {['small', 'medium', 'large'].map((size) => (
                  <TouchableOpacity
                    key={size}
                    style={[
                      styles.sizeButton,
                      overlayConfig.size === size && styles.activeSizeButton
                    ]}
                    onPress={() => setOverlayConfig(prev => ({ ...prev, size: size as any }))}
                  >
                    <Text style={[
                      styles.sizeButtonText,
                      overlayConfig.size === size && styles.activeSizeButtonText
                    ]}>
                      {size.charAt(0).toUpperCase() + size.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Opacity: {Math.round(overlayConfig.opacity * 100)}%</Text>
              {/* Slider would go here - using buttons for demo */}
              <View style={styles.opacityControls}>
                <TouchableOpacity
                  style={styles.opacityButton}
                  onPress={() => setOverlayConfig(prev => ({
                    ...prev,
                    opacity: Math.max(0.1, prev.opacity - 0.1)
                  }))}
                >
                  <Ionicons name="remove" size={20} color={Colors.text.primary} />
                </TouchableOpacity>
                <Text style={styles.opacityValue}>{Math.round(overlayConfig.opacity * 100)}%</Text>
                <TouchableOpacity
                  style={styles.opacityButton}
                  onPress={() => setOverlayConfig(prev => ({
                    ...prev,
                    opacity: Math.min(1.0, prev.opacity + 0.1)
                  }))}
                >
                  <Ionicons name="add" size={20} color={Colors.text.primary} />
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}
      </GlassContainer>

      {/* Display Options */}
      {overlayConfig.enabled && (
        <GlassContainer style={styles.section} intensity="medium">
          <Text style={styles.sectionTitle}>Display Options</Text>

          {[
            { key: 'showCurrentExercise', label: 'Current Exercise' },
            { key: 'showCurrentSet', label: 'Current Set' },
            { key: 'showTimer', label: 'Workout Timer' },
            { key: 'showHeartRate', label: 'Heart Rate' },
            { key: 'showCalories', label: 'Calories Burned' },
            { key: 'showPersonalRecords', label: 'Personal Records' },
            { key: 'showWorkoutProgress', label: 'Workout Progress' },
          ].map((option) => (
            <View key={option.key} style={styles.switchRow}>
              <Text style={styles.switchLabel}>{option.label}</Text>
              <Switch
                value={overlayConfig[option.key as keyof WorkoutOverlayConfig] as boolean}
                onValueChange={(value) => setOverlayConfig(prev => ({
                  ...prev,
                  [option.key]: value
                }))}
                trackColor={{ false: Colors.background.tertiary, true: Colors.accent.primary }}
                thumbColor={Colors.text.inverse}
              />
            </View>
          ))}
        </GlassContainer>
      )}
    </ScrollView>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={Colors.accent.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Stream Manager</Text>
          <TouchableOpacity style={styles.startButton} onPress={startStream}>
            <Text style={styles.startButtonText}>Start</Text>
          </TouchableOpacity>
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabBar}>
          {[
            { key: 'twitch', label: 'Twitch', icon: 'logo-twitch' },
            { key: 'overlay', label: 'Overlay', icon: 'layers' },
            { key: 'preview', label: 'Preview', icon: 'eye' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[styles.tab, activeTab === tab.key && styles.activeTab]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <Ionicons
                name={tab.icon as any}
                size={20}
                color={activeTab === tab.key ? Colors.accent.primary : Colors.text.tertiary}
              />
              <Text style={[
                styles.tabText,
                activeTab === tab.key && styles.activeTabText
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {activeTab === 'twitch' && renderTwitchTab()}
        {activeTab === 'overlay' && renderOverlayTab()}
        {activeTab === 'preview' && (
          <View style={styles.previewContainer}>
            <Text style={styles.previewText}>Overlay Preview Coming Soon</Text>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.primary,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text.primary,
  },
  startButton: {
    backgroundColor: Colors.accent.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  startButtonText: {
    color: Colors.text.inverse,
    fontWeight: '600',
    fontSize: 14,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors.background.secondary,
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: Colors.background.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.tertiary,
    marginLeft: 6,
  },
  activeTabText: {
    color: Colors.accent.primary,
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 20,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginLeft: 8,
    flex: 1,
  },
  connectedSection: {
    marginTop: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  userHandle: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  disconnectButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.status.error,
  },
  disconnectText: {
    color: Colors.status.error,
    fontSize: 12,
    fontWeight: '500',
  },
  connectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#9146FF',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  connectButtonText: {
    color: Colors.text.inverse,
    fontWeight: '600',
    marginLeft: 8,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.secondary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: Colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    color: Colors.text.primary,
    fontSize: 14,
  },
  categorySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.background.tertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  categoryText: {
    color: Colors.text.primary,
    fontSize: 14,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: Colors.accent.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    color: Colors.text.inverse,
    fontSize: 12,
    fontWeight: '500',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 14,
    color: Colors.text.primary,
    flex: 1,
  },
  positionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  positionButton: {
    backgroundColor: Colors.background.tertiary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 80,
    alignItems: 'center',
  },
  activePositionButton: {
    backgroundColor: Colors.accent.primary,
  },
  positionButtonText: {
    color: Colors.text.secondary,
    fontSize: 12,
    fontWeight: '500',
  },
  activePositionButtonText: {
    color: Colors.text.inverse,
  },
  sizeSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  sizeButton: {
    flex: 1,
    backgroundColor: Colors.background.tertiary,
    paddingVertical: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeSizeButton: {
    backgroundColor: Colors.accent.primary,
  },
  sizeButtonText: {
    color: Colors.text.secondary,
    fontSize: 14,
    fontWeight: '500',
  },
  activeSizeButtonText: {
    color: Colors.text.inverse,
  },
  opacityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 20,
  },
  opacityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  opacityValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    minWidth: 50,
    textAlign: 'center',
  },
  previewContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  previewText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
});
