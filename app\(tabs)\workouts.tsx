import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TextInput,
  Modal,
  Animated,
  Dimensions,
  Alert,
  Vibration,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GlassContainer from '../../components/ui/GlassContainer';
import ExerciseLibrary from '../../components/ExerciseLibrary';
import StreamManager from '../../components/streaming/StreamManager';
import LiveWorkoutOverlay from '../../components/streaming/LiveWorkoutOverlay';
import { Colors } from '../../constants/Colors';
import { WorkoutLog, WorkoutAnalytics, Exercise, WorkoutSet, WorkoutExercise } from '../../types/workout';
import { TwitchStreamConfig, WorkoutOverlayConfig, LiveWorkoutData } from '../../types/social';
import WorkoutDataBroadcaster from '../../services/WorkoutDataBroadcaster';

// Mock data for demo
const mockAnalytics: WorkoutAnalytics = {
  totalWorkouts: 127,
  totalHours: 143.5,
  totalVolume: 285420,
  averageWorkoutTime: 68,
  workoutStreak: 12,
  muscleGroupDistribution: {
    'Chest': 25,
    'Back': 22,
    'Legs': 30,
    'Shoulders': 18,
    'Arms': 20,
  },
  strengthProgress: [],
  weeklyStats: [],
};

const mockRecentWorkouts: WorkoutLog[] = [
  {
    id: '1',
    userId: 'user1',
    title: 'Push Day - Chest & Shoulders',
    date: new Date('2024-01-15'),
    startTime: new Date('2024-01-15T10:00:00'),
    endTime: new Date('2024-01-15T11:15:00'),
    exercises: [],
    totalVolume: 12450,
    difficulty: 8,
    isPublic: true,
    likes: 23,
    comments: [],
    tags: ['push', 'chest', 'shoulders'],
  },
  {
    id: '2',
    userId: 'user1',
    title: 'Pull Day - Back & Biceps',
    date: new Date('2024-01-13'),
    startTime: new Date('2024-01-13T09:30:00'),
    endTime: new Date('2024-01-13T10:45:00'),
    exercises: [],
    totalVolume: 11200,
    difficulty: 7,
    isPublic: true,
    likes: 18,
    comments: [],
    tags: ['pull', 'back', 'biceps'],
  },
];

export default function WorkoutsScreen() {
  const [activeTab, setActiveTab] = useState<'log' | 'analytics' | 'history'>('log');
  const [showWorkoutForm, setShowWorkoutForm] = useState(false);
  const [workoutTitle, setWorkoutTitle] = useState('');
  const [selectedExercises, setSelectedExercises] = useState<string[]>([]);

  // Advanced workout tracking state
  const [isWorkoutActive, setIsWorkoutActive] = useState(false);
  const [workoutStartTime, setWorkoutStartTime] = useState<Date | null>(null);
  const [currentWorkout, setCurrentWorkout] = useState<WorkoutExercise[]>([]);
  const [restTimer, setRestTimer] = useState(0);
  const [isRestTimerActive, setIsRestTimerActive] = useState(false);
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [currentSetIndex, setCurrentSetIndex] = useState(0);
  const [workoutNotes, setWorkoutNotes] = useState('');
  const [showExerciseLibrary, setShowExerciseLibrary] = useState(false);
  const [showRestTimer, setShowRestTimer] = useState(false);
  const [personalRecords, setPersonalRecords] = useState<string[]>([]);

  // Live streaming state
  const [showStreamManager, setShowStreamManager] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamConfig, setStreamConfig] = useState<TwitchStreamConfig & { overlayConfig: WorkoutOverlayConfig } | null>(null);
  const [liveWorkoutData, setLiveWorkoutData] = useState<LiveWorkoutData | null>(null);
  const [showOverlayPreview, setShowOverlayPreview] = useState(false);

  // Workout data broadcaster
  const workoutBroadcaster = WorkoutDataBroadcaster.getInstance();

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Mock exercise database for lookup
  const exerciseDatabase: { [key: string]: Exercise } = {
    '1': { id: '1', name: 'Bench Press', category: 'chest', muscleGroups: ['chest', 'triceps'], equipment: ['barbell'], instructions: [], isCustom: false, difficulty: 'intermediate' },
    '2': { id: '2', name: 'Squat', category: 'legs', muscleGroups: ['quadriceps', 'glutes'], equipment: ['barbell'], instructions: [], isCustom: false, difficulty: 'intermediate' },
    '3': { id: '3', name: 'Deadlift', category: 'back', muscleGroups: ['hamstrings', 'glutes', 'back'], equipment: ['barbell'], instructions: [], isCustom: false, difficulty: 'advanced' },
    '4': { id: '4', name: 'Pull-ups', category: 'back', muscleGroups: ['lats', 'biceps'], equipment: ['pull-up bar'], instructions: [], isCustom: false, difficulty: 'intermediate' },
    '5': { id: '5', name: 'Overhead Press', category: 'shoulders', muscleGroups: ['shoulders', 'triceps'], equipment: ['barbell'], instructions: [], isCustom: false, difficulty: 'intermediate' },
  };

  const getExerciseName = (exerciseId: string): string => {
    return exerciseDatabase[exerciseId]?.name || `Exercise ${exerciseId}`;
  };

  // Timer effect for rest periods
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRestTimerActive && restTimer > 0) {
      interval = setInterval(() => {
        setRestTimer((prev) => {
          if (prev <= 1) {
            setIsRestTimerActive(false);
            Vibration.vibrate([0, 500, 200, 500]);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRestTimerActive, restTimer]);

  // Pulse animation for active workout
  useEffect(() => {
    if (isWorkoutActive) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isWorkoutActive]);

  const startWorkout = () => {
    console.log('Starting workout...');
    Alert.alert('Debug', 'Starting workout now!');
    setIsWorkoutActive(true);
    setWorkoutStartTime(new Date());
    setCurrentWorkout([]);
    setWorkoutTitle('');
    setShowWorkoutForm(true);
    console.log('Workout started, form should be visible');

    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const endWorkout = () => {
    if (currentWorkout.length === 0) {
      Alert.alert('Empty Workout', 'Add at least one exercise to save your workout.');
      return;
    }

    Alert.alert(
      'End Workout',
      'Are you sure you want to end this workout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Save & End',
          onPress: () => {
            saveWorkout();
            setIsWorkoutActive(false);
            setShowWorkoutForm(false);
            setCurrentWorkout([]);
            setWorkoutStartTime(null);
          }
        },
      ]
    );
  };

  const saveWorkout = () => {
    // Calculate workout stats
    const endTime = new Date();
    const duration = workoutStartTime ?
      Math.round((endTime.getTime() - workoutStartTime.getTime()) / 60000) : 0;

    const totalVolume = currentWorkout.reduce((total, exercise) => {
      return total + exercise.sets.reduce((setTotal, set) => {
        return setTotal + ((set.weight || 0) * (set.reps || 0));
      }, 0);
    }, 0);

    // Here you would save to your backend/database
    console.log('Saving workout:', {
      title: workoutTitle,
      duration,
      totalVolume,
      exercises: currentWorkout,
      notes: workoutNotes,
    });
  };

  const startRestTimer = (seconds: number = 90) => {
    setRestTimer(seconds);
    setIsRestTimerActive(true);
    setShowRestTimer(true);

    // Update broadcaster if streaming
    if (isStreaming) {
      workoutBroadcaster.startRestTimer(seconds);
    }
  };

  const addExercise = (exercise: Exercise) => {
    console.log('Adding exercise:', exercise.name);
    const newExercise: WorkoutExercise = {
      id: Date.now().toString(),
      exerciseId: exercise.id,
      sets: [{
        id: Date.now().toString() + '_set1',
        exerciseId: exercise.id,
        setNumber: 1,
        reps: undefined,
        weight: undefined,
        setType: 'normal',
        completed: false,
      }],
      notes: '',
    };
    const updatedWorkout = [...currentWorkout, newExercise];
    setCurrentWorkout(updatedWorkout);
    setShowExerciseLibrary(false);
    console.log('Current workout after adding:', updatedWorkout.length, 'exercises');

    // Update broadcaster if streaming
    if (isStreaming) {
      workoutBroadcaster.updateWorkout(updatedWorkout);
    }
  };

  const addSet = (exerciseIndex: number) => {
    const newSet: WorkoutSet = {
      id: Date.now().toString(),
      exerciseId: currentWorkout[exerciseIndex].exerciseId,
      setNumber: currentWorkout[exerciseIndex].sets.length + 1,
      reps: undefined,
      weight: undefined,
      setType: 'normal',
      completed: false,
    };

    const updatedWorkout = [...currentWorkout];
    updatedWorkout[exerciseIndex].sets.push(newSet);
    setCurrentWorkout(updatedWorkout);

    // Update broadcaster if streaming
    if (isStreaming) {
      workoutBroadcaster.updateWorkout(updatedWorkout);
    }
  };

  const updateSet = (exerciseIndex: number, setIndex: number, field: keyof WorkoutSet, value: any) => {
    const updatedWorkout = [...currentWorkout];
    updatedWorkout[exerciseIndex].sets[setIndex] = {
      ...updatedWorkout[exerciseIndex].sets[setIndex],
      [field]: value,
    };
    setCurrentWorkout(updatedWorkout);

    // Update broadcaster if streaming
    if (isStreaming) {
      workoutBroadcaster.updateWorkout(updatedWorkout);
      workoutBroadcaster.updateCurrentPosition(exerciseIndex, setIndex);
    }
  };

  const completeSet = (exerciseIndex: number, setIndex: number) => {
    updateSet(exerciseIndex, setIndex, 'completed', true);

    // Check for personal record
    const set = currentWorkout[exerciseIndex].sets[setIndex];
    if (set.weight && set.reps) {
      const volume = set.weight * set.reps;
      // Here you would check against historical data for PRs
      if (volume > 100) { // Mock PR threshold
        const prRecord = `${set.weight}kg x ${set.reps}`;
        setPersonalRecords([...personalRecords, prRecord]);
        Vibration.vibrate([0, 200, 100, 200]);

        // Add to broadcaster if streaming
        if (isStreaming) {
          workoutBroadcaster.addPersonalRecord(prRecord);
        }
      }
    }

    // Auto-start rest timer
    startRestTimer();
  };

  // Streaming Functions
  const handleStartStream = async (config: TwitchStreamConfig & { overlayConfig: WorkoutOverlayConfig }) => {
    try {
      setStreamConfig(config);
      setIsStreaming(true);

      // Start workout data broadcaster
      await workoutBroadcaster.startBroadcasting({
        streamId: Date.now().toString(),
        overlayConfig: config.overlayConfig,
        broadcastInterval: 1000, // Update every second
        enableTwitchIntegration: config.enabled,
      });

      // Add listener for workout data updates
      workoutBroadcaster.addListener('workouts-screen', (data: LiveWorkoutData) => {
        setLiveWorkoutData(data);
      });

      // Update broadcaster with current workout state
      if (currentWorkout.length > 0) {
        workoutBroadcaster.updateWorkout(currentWorkout);
        workoutBroadcaster.updateCurrentPosition(currentExerciseIndex, currentSetIndex);
      }

      Alert.alert('Stream Started', 'Your workout is now being streamed live!');
    } catch (error) {
      console.error('Error starting stream:', error);
      Alert.alert('Error', 'Failed to start stream. Please try again.');
      setIsStreaming(false);
      setStreamConfig(null);
    }
  };

  const stopStream = () => {
    Alert.alert(
      'Stop Stream',
      'Are you sure you want to stop streaming?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Stop Stream',
          style: 'destructive',
          onPress: () => {
            workoutBroadcaster.stopBroadcasting();
            workoutBroadcaster.removeListener('workouts-screen');
            setIsStreaming(false);
            setStreamConfig(null);
            setLiveWorkoutData(null);
            Alert.alert('Stream Stopped', 'Your live stream has ended.');
          },
        },
      ]
    );
  };

  const toggleOverlayPreview = () => {
    setShowOverlayPreview(!showOverlayPreview);
  };

  const renderAnalyticsCard = (title: string, value: string, subtitle?: string, icon?: string) => (
    <GlassContainer style={styles.analyticsCard} intensity="medium">
      <View style={styles.cardContent}>
        <View style={styles.cardHeader}>
          {icon && <Ionicons name={icon as any} size={24} color={Colors.accent.primary} />}
          <Text style={styles.cardTitle}>{title}</Text>
        </View>
        <Text style={styles.cardValue}>{value}</Text>
        {subtitle && <Text style={styles.cardSubtitle}>{subtitle}</Text>}
      </View>
    </GlassContainer>
  );

  const renderWorkoutItem = ({ item }: { item: WorkoutLog }) => (
    <GlassContainer style={styles.workoutItem} intensity="light">
      <View style={styles.workoutHeader}>
        <View style={styles.workoutInfo}>
          <Text style={styles.workoutTitle}>{item.title}</Text>
                     <Text style={styles.workoutDate}>
             {item.date.toLocaleDateString()} • {Math.round(((item.endTime?.getTime() || item.startTime.getTime()) - item.startTime.getTime()) / 60000)} min
           </Text>
        </View>
        <View style={styles.workoutStats}>
          <Text style={styles.workoutVolume}>{item.totalVolume.toLocaleString()} kg</Text>
          <View style={styles.difficultyBadge}>
            <Text style={styles.difficultyText}>{item.difficulty}/10</Text>
          </View>
        </View>
      </View>
      <View style={styles.workoutFooter}>
        <View style={styles.workoutTags}>
          {item.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>
        <View style={styles.workoutEngagement}>
          <View style={styles.engagementItem}>
            <Ionicons name="heart-outline" size={16} color={Colors.text.tertiary} />
            <Text style={styles.engagementText}>{item.likes}</Text>
          </View>
        </View>
      </View>
    </GlassContainer>
  );

  const renderWorkoutForm = () => (
    <Modal
      visible={showWorkoutForm}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <SafeAreaView style={styles.modalContainer}>
        {/* Workout Header */}
        <View style={styles.workoutHeader}>
          <View style={styles.workoutHeaderTop}>
            <TouchableOpacity onPress={endWorkout}>
              <Ionicons name="close" size={24} color={Colors.accent.primary} />
            </TouchableOpacity>
            <View style={styles.workoutTimer}>
              <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                <Ionicons name="time" size={20} color={Colors.accent.primary} />
              </Animated.View>
              <Text style={styles.timerText}>
                {workoutStartTime ?
                  Math.floor((Date.now() - workoutStartTime.getTime()) / 60000) : 0} min
              </Text>
            </View>
            <View style={styles.headerActions}>
              {/* Stream Controls */}
              {isStreaming ? (
                <TouchableOpacity style={styles.streamingButton} onPress={stopStream}>
                  <Ionicons name="stop-circle" size={20} color={Colors.status.error} />
                  <Text style={styles.streamingText}>LIVE</Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity style={styles.streamButton} onPress={() => setShowStreamManager(true)}>
                  <Ionicons name="videocam" size={20} color={Colors.accent.primary} />
                </TouchableOpacity>
              )}

              <TouchableOpacity style={styles.finishButton} onPress={endWorkout}>
                <Text style={styles.finishButtonText}>Finish</Text>
              </TouchableOpacity>
            </View>
          </View>

          <TextInput
            style={styles.workoutTitleInput}
            value={workoutTitle}
            onChangeText={setWorkoutTitle}
            placeholder="Workout Name"
            placeholderTextColor={Colors.text.tertiary}
          />

          {personalRecords.length > 0 && (
            <View style={styles.prBanner}>
              <Ionicons name="trophy" size={16} color={Colors.accent.secondary} />
              <Text style={styles.prText}>
                {personalRecords.length} Personal Record{personalRecords.length > 1 ? 's' : ''}!
              </Text>
            </View>
          )}
        </View>

        {/* Exercise List */}
        <ScrollView style={styles.exerciseList} showsVerticalScrollIndicator={false}>
          {currentWorkout.map((exercise, exerciseIndex) => (
            <GlassContainer key={exercise.id} style={styles.exerciseContainer} intensity="medium">
              <View style={styles.exerciseHeader}>
                <Text style={styles.exerciseName}>
                  {getExerciseName(exercise.exerciseId)}
                </Text>
                <TouchableOpacity>
                  <Ionicons name="information-circle-outline" size={20} color={Colors.text.tertiary} />
                </TouchableOpacity>
              </View>

              {/* Sets */}
              <View style={styles.setsContainer}>
                <View style={styles.setsHeader}>
                  <Text style={styles.setHeaderText}>SET</Text>
                  <Text style={styles.setHeaderText}>PREVIOUS</Text>
                  <Text style={styles.setHeaderText}>KG</Text>
                  <Text style={styles.setHeaderText}>REPS</Text>
                  <Text style={styles.setHeaderText}>✓</Text>
                </View>

                {exercise.sets.map((set, setIndex) => (
                  <View key={set.id} style={styles.setRow}>
                    <Text style={styles.setNumber}>{setIndex + 1}</Text>
                    <Text style={styles.previousText}>
                      {/* Mock previous data */}
                      {setIndex === 0 ? '80kg × 8' : '80kg × 6'}
                    </Text>
                    <TextInput
                      style={[styles.setInput, set.completed && styles.completedInput]}
                      value={set.weight?.toString() || ''}
                      onChangeText={(text) => updateSet(exerciseIndex, setIndex, 'weight', parseFloat(text) || 0)}
                      placeholder="0"
                      placeholderTextColor={Colors.text.tertiary}
                      keyboardType="numeric"
                    />
                    <TextInput
                      style={[styles.setInput, set.completed && styles.completedInput]}
                      value={set.reps?.toString() || ''}
                      onChangeText={(text) => updateSet(exerciseIndex, setIndex, 'reps', parseInt(text) || 0)}
                      placeholder="0"
                      placeholderTextColor={Colors.text.tertiary}
                      keyboardType="numeric"
                    />
                    <TouchableOpacity
                      style={[styles.checkButton, set.completed && styles.checkedButton]}
                      onPress={() => completeSet(exerciseIndex, setIndex)}
                    >
                      <Ionicons
                        name={set.completed ? "checkmark" : "checkmark-outline"}
                        size={20}
                        color={set.completed ? Colors.text.inverse : Colors.text.tertiary}
                      />
                    </TouchableOpacity>
                  </View>
                ))}

                <TouchableOpacity
                  style={styles.addSetButton}
                  onPress={() => addSet(exerciseIndex)}
                >
                  <Ionicons name="add" size={20} color={Colors.accent.primary} />
                  <Text style={styles.addSetText}>Add Set</Text>
                </TouchableOpacity>
              </View>
            </GlassContainer>
          ))}

          {/* Add Exercise Button */}
          <TouchableOpacity
            style={styles.addExerciseButton}
            onPress={() => {
              console.log('Add Exercise button pressed');
              Alert.alert('Debug', 'Add Exercise button was pressed!');
              setShowExerciseLibrary(true);
            }}
          >
            <Ionicons name="add-circle-outline" size={24} color={Colors.accent.primary} />
            <Text style={styles.addExerciseText}>Add Exercise</Text>
          </TouchableOpacity>

          {/* Test Button - Add Bench Press Directly */}
          <TouchableOpacity
            style={[styles.addExerciseButton, { backgroundColor: Colors.accent.secondary }]}
            onPress={() => {
              const testExercise = {
                id: '1',
                name: 'Bench Press',
                category: 'chest' as const,
                muscleGroups: ['chest', 'triceps'],
                equipment: ['barbell'],
                instructions: [],
                isCustom: false,
                difficulty: 'intermediate' as const
              };
              addExercise(testExercise);
            }}
          >
            <Ionicons name="fitness-outline" size={24} color={Colors.text.inverse} />
            <Text style={[styles.addExerciseText, { color: Colors.text.inverse }]}>Test: Add Bench Press</Text>
          </TouchableOpacity>

          {/* Helper text when no exercises */}
          {currentWorkout.length === 0 && (
            <GlassContainer style={styles.helperContainer} intensity="light">
              <Text style={styles.helperText}>
                👆 Tap "Add Exercise" above to start building your workout
              </Text>
            </GlassContainer>
          )}

          <View style={{ height: 100 }} />
        </ScrollView>

        {/* Rest Timer Overlay */}
        {showRestTimer && (
          <View style={styles.restTimerOverlay}>
            <GlassContainer style={styles.restTimerContainer} intensity="strong">
              <Text style={styles.restTimerTitle}>Rest Timer</Text>
              <Text style={styles.restTimerTime}>
                {Math.floor(restTimer / 60)}:{(restTimer % 60).toString().padStart(2, '0')}
              </Text>
              <View style={styles.restTimerButtons}>
                <TouchableOpacity
                  style={styles.restTimerButton}
                  onPress={() => setRestTimer(restTimer + 30)}
                >
                  <Text style={styles.restTimerButtonText}>+30s</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.restTimerButton}
                  onPress={() => {
                    setIsRestTimerActive(false);
                    setShowRestTimer(false);
                  }}
                >
                  <Text style={styles.restTimerButtonText}>Skip</Text>
                </TouchableOpacity>
              </View>
            </GlassContainer>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );

  const renderLogTab = () => (
    <View style={styles.tabContent}>
      <GlassContainer style={styles.quickStartContainer} intensity="strong">
        <View style={styles.quickStartHeader}>
          <Text style={styles.sectionTitle}>Quick Start</Text>
          <Text style={styles.streakText}>🔥 {mockAnalytics.workoutStreak} day streak</Text>
        </View>
        <View style={styles.quickStartButtons}>
          <TouchableOpacity
            style={[styles.primaryButton, isWorkoutActive && styles.activeWorkoutButton]}
            onPress={isWorkoutActive ? () => setShowWorkoutForm(true) : startWorkout}
          >
            <Animated.View style={{ transform: [{ scale: isWorkoutActive ? pulseAnim : 1 }] }}>
              <Ionicons
                name={isWorkoutActive ? "fitness" : "play"}
                size={24}
                color={Colors.text.inverse}
              />
            </Animated.View>
            <Text style={styles.primaryButtonText}>
              {isWorkoutActive ? 'Continue Workout' : 'Start Empty Workout'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.secondaryButton}>
            <Ionicons name="library-outline" size={24} color={Colors.accent.primary} />
            <Text style={styles.secondaryButtonText}>Choose Template</Text>
          </TouchableOpacity>
        </View>
      </GlassContainer>

      <View style={styles.analyticsGrid}>
        {renderAnalyticsCard('Total Volume', '285k kg', 'This month', 'barbell-outline')}
        {renderAnalyticsCard('Workouts', '127', 'All time', 'fitness-outline')}
        {renderAnalyticsCard('Hours Trained', '143.5h', 'Total time', 'time-outline')}
        {renderAnalyticsCard('Average Time', '68 min', 'Per workout', 'stopwatch-outline')}
      </View>
    </View>
  );

  const renderAnalyticsTab = () => (
    <View style={styles.tabContent}>
      <GlassContainer style={styles.chartContainer} intensity="medium">
        <Text style={styles.sectionTitle}>Muscle Group Distribution</Text>
        <View style={styles.muscleChart}>
          {Object.entries(mockAnalytics.muscleGroupDistribution).map(([muscle, sets]) => (
            <View key={muscle} style={styles.muscleItem}>
              <View style={styles.muscleRow}>
                <Text style={styles.muscleName}>{muscle}</Text>
                <Text style={styles.muscleValue}>{sets} sets</Text>
              </View>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${(sets / 30) * 100}%` }
                  ]}
                />
              </View>
            </View>
          ))}
        </View>
      </GlassContainer>
    </View>
  );

  const renderHistoryTab = () => (
    <View style={styles.tabContent}>
      <FlatList
        data={mockRecentWorkouts}
        renderItem={renderWorkoutItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.historyList}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderWorkoutForm()}
      <ExerciseLibrary
        visible={showExerciseLibrary}
        onClose={() => {
          console.log('Closing exercise library');
          Alert.alert('Debug', 'Exercise library closed');
          setShowExerciseLibrary(false);
        }}
        onSelectExercise={addExercise}
      />

      {/* Stream Manager Modal */}
      <StreamManager
        visible={showStreamManager}
        onClose={() => setShowStreamManager(false)}
        onStartStream={handleStartStream}
      />

      {/* Live Workout Overlay Preview */}
      {showOverlayPreview && liveWorkoutData && streamConfig?.overlayConfig && (
        <LiveWorkoutOverlay
          workoutData={liveWorkoutData}
          config={streamConfig.overlayConfig}
          isVisible={true}
        />
      )}

      {/* Debug info */}
      {__DEV__ && (
        <View style={{ position: 'absolute', top: 100, right: 10, backgroundColor: 'red', padding: 10 }}>
          <Text style={{ color: 'white', fontSize: 12 }}>
            ExerciseLib: {showExerciseLibrary ? 'OPEN' : 'CLOSED'}
          </Text>
          <Text style={{ color: 'white', fontSize: 12 }}>
            WorkoutForm: {showWorkoutForm ? 'OPEN' : 'CLOSED'}
          </Text>
        </View>
      )}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Workouts</Text>
        <TouchableOpacity style={styles.headerButton}>
          <Ionicons name="settings-outline" size={24} color={Colors.accent.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.tabBar}>
        {[
          { key: 'log', title: 'Log', icon: 'add-circle-outline' },
          { key: 'analytics', title: 'Analytics', icon: 'analytics-outline' },
          { key: 'history', title: 'History', icon: 'time-outline' },
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, activeTab === tab.key && styles.activeTab]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <Ionicons
              name={tab.icon as any}
              size={20}
              color={activeTab === tab.key ? Colors.accent.primary : Colors.text.tertiary}
            />
            <Text style={[
              styles.tabText,
              activeTab === tab.key && styles.activeTabText
            ]}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'log' && renderLogTab()}
        {activeTab === 'analytics' && renderAnalyticsTab()}
        {activeTab === 'history' && renderHistoryTab()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  headerButton: {
    padding: 8,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors.background.secondary,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: Colors.glass.medium,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.tertiary,
    marginLeft: 6,
  },
  activeTabText: {
    color: Colors.accent.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  tabContent: {
    paddingBottom: 100,
  },
  quickStartContainer: {
    marginBottom: 20,
  },
  quickStartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  streakText: {
    fontSize: 14,
    color: Colors.accent.primary,
    fontWeight: '600',
  },
  quickStartButtons: {
    gap: 12,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.accent.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.inverse,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.accent.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.accent.primary,
  },
  analyticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  analyticsCard: {
    width: '48%',
  },
  cardContent: {
    gap: 8,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cardTitle: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontWeight: '600',
  },
  cardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  cardSubtitle: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  chartContainer: {
    marginBottom: 20,
  },
  muscleChart: {
    gap: 16,
    marginTop: 16,
  },
  muscleItem: {
    gap: 8,
  },
  muscleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  muscleName: {
    fontSize: 16,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  muscleValue: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.background.tertiary,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.accent.primary,
    borderRadius: 3,
  },
  historyList: {
    gap: 12,
  },
  workoutItem: {
    gap: 12,
  },
  workoutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  workoutInfo: {
    flex: 1,
    gap: 4,
  },
  workoutTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  workoutDate: {
    fontSize: 14,
    color: Colors.text.tertiary,
  },
  workoutStats: {
    alignItems: 'flex-end',
    gap: 8,
  },
  workoutVolume: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.accent.primary,
  },
  difficultyBadge: {
    backgroundColor: Colors.glass.medium,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 12,
    color: Colors.text.secondary,
    fontWeight: '600',
  },
  workoutFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  workoutTags: {
    flexDirection: 'row',
    gap: 6,
    flex: 1,
  },
  tag: {
    backgroundColor: Colors.background.tertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 12,
    color: Colors.text.tertiary,
    fontWeight: '500',
  },
  workoutEngagement: {
    flexDirection: 'row',
    gap: 12,
  },
  engagementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  engagementText: {
    fontSize: 12,
    color: Colors.text.tertiary,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.primary,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  saveButton: {
    backgroundColor: Colors.accent.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.inverse,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formSection: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  textInput: {
    backgroundColor: Colors.background.tertiary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.text.primary,
  },
  addExerciseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.accent.primary,
    borderStyle: 'dashed',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  addExerciseText: {
    fontSize: 16,
    color: Colors.accent.primary,
    fontWeight: '600',
  },
  helperContainer: {
    marginTop: 20,
    alignItems: 'center',
    paddingVertical: 16,
  },
  helperText: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Advanced workout tracking styles
  activeWorkoutButton: {
    backgroundColor: Colors.accent.secondary,
  },
  workoutHeaderTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  workoutTimer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  timerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.accent.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  streamButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
  },
  streamingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: Colors.status.error,
    gap: 6,
  },
  streamingText: {
    fontSize: 12,
    fontWeight: '700',
    color: Colors.text.inverse,
    letterSpacing: 1,
  },
  finishButton: {
    backgroundColor: Colors.accent.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  finishButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.inverse,
  },
  workoutTitleInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
    backgroundColor: 'transparent',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.primary,
    paddingVertical: 8,
    marginBottom: 16,
  },
  prBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.glass.medium,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  prText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.accent.secondary,
  },
  exerciseList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  exerciseContainer: {
    marginBottom: 20,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  exerciseName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  setsContainer: {
    gap: 8,
  },
  setsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
    backgroundColor: Colors.background.tertiary,
    borderRadius: 6,
  },
  setHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text.tertiary,
    flex: 1,
    textAlign: 'center',
  },
  setRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 12,
    backgroundColor: Colors.glass.light,
    borderRadius: 6,
  },
  setNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.secondary,
    flex: 1,
    textAlign: 'center',
  },
  previousText: {
    fontSize: 14,
    color: Colors.text.tertiary,
    flex: 1,
    textAlign: 'center',
  },
  setInput: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 8,
    fontSize: 16,
    color: Colors.text.primary,
    textAlign: 'center',
    marginHorizontal: 4,
  },
  completedInput: {
    backgroundColor: Colors.glass.medium,
    borderColor: Colors.accent.primary,
  },
  checkButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
    marginHorizontal: 4,
  },
  checkedButton: {
    backgroundColor: Colors.accent.primary,
    borderColor: Colors.accent.primary,
  },
  addSetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: Colors.accent.primary,
    borderStyle: 'dashed',
    borderRadius: 6,
    gap: 6,
    marginTop: 8,
  },
  addSetText: {
    fontSize: 14,
    color: Colors.accent.primary,
    fontWeight: '600',
  },
  restTimerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  restTimerContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
    marginHorizontal: 40,
  },
  restTimerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  restTimerTime: {
    fontSize: 48,
    fontWeight: 'bold',
    color: Colors.accent.primary,
    marginBottom: 24,
  },
  restTimerButtons: {
    flexDirection: 'row',
    gap: 16,
  },
  restTimerButton: {
    backgroundColor: Colors.glass.medium,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.accent.primary,
  },
  restTimerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.accent.primary,
  },
});