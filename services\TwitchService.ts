import { TwitchStreamConfig, LiveWorkoutData } from '../types/social';

export interface TwitchAuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  scope: string[];
  token_type: string;
}

export interface TwitchStreamInfo {
  id: string;
  user_id: string;
  user_login: string;
  user_name: string;
  game_id: string;
  game_name: string;
  type: string;
  title: string;
  viewer_count: number;
  started_at: string;
  language: string;
  thumbnail_url: string;
  tag_ids: string[];
  is_mature: boolean;
}

export interface TwitchUser {
  id: string;
  login: string;
  display_name: string;
  type: string;
  broadcaster_type: string;
  description: string;
  profile_image_url: string;
  offline_image_url: string;
  view_count: number;
  created_at: string;
}

class TwitchService {
  private static instance: TwitchService;
  private clientId: string = process.env.EXPO_PUBLIC_TWITCH_CLIENT_ID || '';
  private clientSecret: string = process.env.EXPO_PUBLIC_TWITCH_CLIENT_SECRET || '';
  private redirectUri: string = process.env.EXPO_PUBLIC_TWITCH_REDIRECT_URI || '';
  private baseUrl: string = 'https://api.twitch.tv/helix';
  private authUrl: string = 'https://id.twitch.tv/oauth2';
  
  // WebSocket connection for real-time data
  private overlayWebSocket: WebSocket | null = null;
  private overlayUrl: string = process.env.EXPO_PUBLIC_OVERLAY_WEBSOCKET_URL || 'ws://localhost:8080';

  public static getInstance(): TwitchService {
    if (!TwitchService.instance) {
      TwitchService.instance = new TwitchService();
    }
    return TwitchService.instance;
  }

  /**
   * Generate Twitch OAuth URL for authentication
   */
  generateAuthUrl(scopes: string[] = ['channel:manage:broadcast', 'chat:read', 'chat:edit']): string {
    const scopeString = scopes.join(' ');
    const state = Math.random().toString(36).substring(2, 15);
    
    return `${this.authUrl}/authorize?` +
      `client_id=${this.clientId}&` +
      `redirect_uri=${encodeURIComponent(this.redirectUri)}&` +
      `response_type=code&` +
      `scope=${encodeURIComponent(scopeString)}&` +
      `state=${state}`;
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code: string): Promise<TwitchAuthResponse> {
    try {
      const response = await fetch(`${this.authUrl}/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          code: code,
          grant_type: 'authorization_code',
          redirect_uri: this.redirectUri,
        }),
      });

      if (!response.ok) {
        throw new Error(`Token exchange failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error exchanging code for token:', error);
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string): Promise<TwitchAuthResponse> {
    try {
      const response = await fetch(`${this.authUrl}/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          refresh_token: refreshToken,
          grant_type: 'refresh_token',
        }),
      });

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(accessToken: string): Promise<TwitchUser> {
    try {
      const response = await fetch(`${this.baseUrl}/users`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Client-Id': this.clientId,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get user info: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data[0];
    } catch (error) {
      console.error('Error getting user info:', error);
      throw error;
    }
  }

  /**
   * Get current stream information
   */
  async getStreamInfo(accessToken: string, userId: string): Promise<TwitchStreamInfo | null> {
    try {
      const response = await fetch(`${this.baseUrl}/streams?user_id=${userId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Client-Id': this.clientId,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get stream info: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data.length > 0 ? data.data[0] : null;
    } catch (error) {
      console.error('Error getting stream info:', error);
      throw error;
    }
  }

  /**
   * Update stream information
   */
  async updateStreamInfo(
    accessToken: string,
    userId: string,
    title?: string,
    categoryId?: string,
    tags?: string[]
  ): Promise<void> {
    try {
      const body: any = {};
      if (title) body.title = title;
      if (categoryId) body.game_id = categoryId;
      if (tags) body.tags = tags;

      const response = await fetch(`${this.baseUrl}/channels?broadcaster_id=${userId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Client-Id': this.clientId,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new Error(`Failed to update stream info: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error updating stream info:', error);
      throw error;
    }
  }

  /**
   * Connect to overlay WebSocket for real-time workout data
   */
  connectToOverlay(streamId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.overlayWebSocket = new WebSocket(`${this.overlayUrl}/stream/${streamId}`);
        
        this.overlayWebSocket.onopen = () => {
          console.log('Connected to overlay WebSocket');
          resolve();
        };

        this.overlayWebSocket.onerror = (error) => {
          console.error('Overlay WebSocket error:', error);
          reject(error);
        };

        this.overlayWebSocket.onclose = () => {
          console.log('Overlay WebSocket connection closed');
          this.overlayWebSocket = null;
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Send workout data to overlay
   */
  sendWorkoutDataToOverlay(workoutData: LiveWorkoutData): void {
    if (this.overlayWebSocket && this.overlayWebSocket.readyState === WebSocket.OPEN) {
      this.overlayWebSocket.send(JSON.stringify({
        type: 'workout_data',
        data: workoutData,
      }));
    } else {
      console.warn('Overlay WebSocket not connected');
    }
  }

  /**
   * Send overlay configuration update
   */
  sendOverlayConfig(streamId: string, config: any): void {
    if (this.overlayWebSocket && this.overlayWebSocket.readyState === WebSocket.OPEN) {
      this.overlayWebSocket.send(JSON.stringify({
        type: 'overlay_config',
        streamId,
        config,
      }));
    }
  }

  /**
   * Disconnect from overlay WebSocket
   */
  disconnectFromOverlay(): void {
    if (this.overlayWebSocket) {
      this.overlayWebSocket.close();
      this.overlayWebSocket = null;
    }
  }

  /**
   * Validate access token
   */
  async validateToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.authUrl}/validate`, {
        headers: {
          'Authorization': `OAuth ${accessToken}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Error validating token:', error);
      return false;
    }
  }

  /**
   * Get Twitch game categories for fitness
   */
  async getFitnessCategories(accessToken: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/games?name=Just%20Chatting&name=Sports&name=Fitness`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Client-Id': this.clientId,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get categories: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error getting fitness categories:', error);
      return [];
    }
  }
}

export default TwitchService;
